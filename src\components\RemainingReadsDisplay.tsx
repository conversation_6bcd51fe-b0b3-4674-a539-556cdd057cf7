import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { getBrowserFingerprint } from '../utils/fingerprint';
import { checkAnonymousEligibility } from '../services/anonymousService';
import { getFontClass } from '../utils/fontUtils';

interface User {
  vipStatus: string;
  remainingReads: number;
}

interface RemainingReadsDisplayProps {
  user: User | null;
  className?: string;
}

const RemainingReadsDisplay: React.FC<RemainingReadsDisplayProps> = ({
  user,
  className = ''
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  const [anonymousReadsUsed, setAnonymousReadsUsed] = useState(false);
  const [loading, setLoading] = useState(true);
  const isDark = theme === 'dark';

  // 检查匿名用户状态
  useEffect(() => {
    const checkAnonymousStatus = async () => {
      if (!user) {
        try {
          const fingerprint = await getBrowserFingerprint();
          const eligibility = await checkAnonymousEligibility(fingerprint);
          setAnonymousReadsUsed(eligibility.hasUsed);
        } catch (error) {
          console.error('检查匿名用户状态失败:', error);
          setAnonymousReadsUsed(false);
        }
      }
      setLoading(false);
    };

    checkAnonymousStatus();
  }, [user]);

  const handleLoginClick = () => {
    // 直接跳转到登录页面
    navigate('/login');
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`animate-pulse w-48 h-10 rounded-full ${isDark ? 'bg-gray-700' : 'bg-gray-200'}`}></div>
      </div>
    );
  }

  if (!user) {
    // 未登录状态 - 无论剩余次数是多少，都显示登录按钮
    const remainingCount = anonymousReadsUsed ? 0 : 1;
    const isEnOrJa = i18n.language.startsWith('en') || i18n.language.startsWith('ja');

    if (isEnOrJa) {
      // 英文/日文环境下的响应式布局：移动端两行，PC端一行
      return (
        <div className={`flex items-center justify-center ${className}`}>
          <div className={`flex items-center gap-3 md:gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
            ${isDark
              ? 'bg-purple-900/30 text-purple-200 border border-purple-500/30 hover:bg-purple-900/40'
              : 'bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100'
            }`}>
            <div className="flex items-center gap-2">
              <svg
                className={`w-4 h-4 flex-shrink-0 ${isDark ? 'text-purple-300' : 'text-purple-600'}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
              {/* 移动端：两行显示，PC端：一行显示 */}
              <div className="flex flex-col md:flex-row md:items-center md:gap-1 leading-tight">
                <span className="text-xs md:text-sm">
                  {i18n.language.startsWith('en') ? 'Remaining' : '残り占い回数'}
                </span>
                <span className="text-xs md:text-sm font-semibold">
                  {i18n.language.startsWith('en')
                    ? `${remainingCount} Reading${remainingCount !== 1 ? 's' : ''}`
                    : `${remainingCount}回`
                  }
                </span>
              </div>
            </div>
            <button
              onClick={handleLoginClick}
              className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 flex-shrink-0 whitespace-nowrap ${
                isDark
                  ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-purple-500/20'
                  : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-md hover:shadow-purple-500/20'
              }`}
            >
              {t('anonymous.login_for_more', '登录解锁更多占卜次数')}
            </button>
          </div>
        </div>
      );
    }

    // 中文环境下保持原有布局
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
          ${isDark
            ? 'bg-purple-900/30 text-purple-200 border border-purple-500/30 hover:bg-purple-900/40'
            : 'bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100'
          }`}>
          <svg
            className={`w-4 h-4 ${isDark ? 'text-purple-300' : 'text-purple-600'}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
          <span>
            {t('common.remaining_reads', '剩余占卜次数：{{count}}次', { count: remainingCount })}
          </span>
          <button
            onClick={handleLoginClick}
            className={`ml-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
              isDark
                ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-purple-500/20'
                : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-md hover:shadow-purple-500/20'
            }`}
          >
            {t('anonymous.login_for_more', '登录解锁更多占卜次数')}
          </button>
        </div>
      </div>
    );
  }
  
  // 已登录状态
  if (user.vipStatus === 'active') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
          ${isDark
            ? 'bg-purple-900/30 text-purple-200 border border-purple-500/30 hover:bg-purple-900/40'
            : 'bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100'
          }`}>
          <svg
            className={`w-4 h-4 ${isDark ? 'text-purple-300' : 'text-purple-600'}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
          <span>
            {t('common.remaining_reads_vip', 'VIP用户 - 无限次占卜')}
          </span>
        </div>
      </div>
    );
  }

  const remainingReads = Math.max(0, user.remainingReads);
  const isEnOrJa = i18n.language.startsWith('en') || i18n.language.startsWith('ja');

  if (isEnOrJa) {
    // 英文/日文环境下的响应式布局：移动端两行，PC端一行
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`flex items-center gap-3 md:gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
          ${isDark
            ? 'bg-purple-900/30 text-purple-200 border border-purple-500/30 hover:bg-purple-900/40'
            : 'bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100'
          }`}>
          <div className="flex items-center gap-2">
            <svg
              className={`w-4 h-4 flex-shrink-0 ${isDark ? 'text-purple-300' : 'text-purple-600'}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
            {/* 移动端：两行显示，PC端：一行显示 */}
            <div className="flex flex-col md:flex-row md:items-center md:gap-1 leading-tight">
              <span className="text-xs md:text-sm">
                {i18n.language.startsWith('en') ? 'Remaining' : '残り占い回数'}
              </span>
              <span className="text-xs md:text-sm font-semibold">
                {i18n.language.startsWith('en')
                  ? `${remainingReads} Reading${remainingReads !== 1 ? 's' : ''}`
                  : `${remainingReads}回`
                }
              </span>
            </div>
          </div>
          {/* 普通用户显示解锁按钮 */}
          <button
            onClick={() => navigate('/membership')}
            className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 flex-shrink-0 whitespace-nowrap ${
              isDark
                ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-purple-500/20'
                : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-md hover:shadow-purple-500/20'
            }`}
          >
            {t('user.unlock_more_reads', '解锁更多占卜次数')}
          </button>
        </div>
      </div>
    );
  }

  // 中文环境下保持原有布局
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
        ${isDark
          ? 'bg-purple-900/30 text-purple-200 border border-purple-500/30 hover:bg-purple-900/40'
          : 'bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100'
        }`}>
        <svg
          className={`w-4 h-4 ${isDark ? 'text-purple-300' : 'text-purple-600'}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
        <span>
          {t('common.remaining_reads', '剩余占卜次数：{{count}}次', { count: remainingReads })}
        </span>
        {/* 普通用户显示解锁按钮 */}
        <button
          onClick={() => navigate('/membership')}
          className={`ml-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
            isDark
              ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-purple-500/20'
              : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-md hover:shadow-purple-500/20'
          }`}
        >
          {t('user.unlock_more_reads', '解锁更多占卜次数')}
        </button>
      </div>
    </div>
  );
};

export default RemainingReadsDisplay;
